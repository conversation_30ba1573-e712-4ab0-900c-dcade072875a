package com.thinkgem.jeesite.modules.sync.entity;

import java.util.Date;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.thinkgem.jeesite.common.persistence.DataEntity;

/**
 * 主体信息实体类
 * <AUTHOR>
 * @version 2025-01-01
 */
public class SubjectInfo extends DataEntity<SubjectInfo> {

    private static final long serialVersionUID = 1L;
    
    private String externalId;          // 外部系统主体ID
    private String subjectName;         // 主体名称
    private String subjectType;         // 主体类型
    private String socialCreditCode;    // 统一社会信用代码
    private String legalPerson;         // 法定代表人
    private String contactPhone;        // 联系电话
    private String contactAddress;      // 联系地址
    private String businessScope;       // 经营范围
    private Date registrationDate;      // 注册日期
    private String status;              // 状态(1:正常 0:停用)
    private String externalStatus;      // 外部系统状态
    private Date lastSyncTime;          // 最后同步时间
    private Date externalUpdateTime;    // 外部系统更新时间
    private Long syncVersion;           // 同步版本号
    private String dataSource;          // 数据来源
    private String rawData;             // 原始JSON数据

    public SubjectInfo() {
        super();
        this.status = "1";
        this.syncVersion = 0L;
        this.dataSource = "external";
    }

    public SubjectInfo(String id) {
        super(id);
    }

    @NotNull(message = "外部系统ID不能为空")
    @Length(min = 1, max = 100, message = "外部系统ID长度必须介于 1 和 100 之间")
    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    @NotNull(message = "主体名称不能为空")
    @Length(min = 1, max = 200, message = "主体名称长度必须介于 1 和 200 之间")
    public String getSubjectName() {
        return subjectName;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    @Length(min = 0, max = 50, message = "主体类型长度必须介于 0 和 50 之间")
    public String getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(String subjectType) {
        this.subjectType = subjectType;
    }

    @Length(min = 0, max = 50, message = "统一社会信用代码长度必须介于 0 和 50 之间")
    public String getSocialCreditCode() {
        return socialCreditCode;
    }

    public void setSocialCreditCode(String socialCreditCode) {
        this.socialCreditCode = socialCreditCode;
    }

    @Length(min = 0, max = 100, message = "法定代表人长度必须介于 0 和 100 之间")
    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    @Length(min = 0, max = 50, message = "联系电话长度必须介于 0 和 50 之间")
    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    @Length(min = 0, max = 500, message = "联系地址长度必须介于 0 和 500 之间")
    public String getContactAddress() {
        return contactAddress;
    }

    public void setContactAddress(String contactAddress) {
        this.contactAddress = contactAddress;
    }

    public String getBusinessScope() {
        return businessScope;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(Date registrationDate) {
        this.registrationDate = registrationDate;
    }

    @Length(min = 0, max = 20, message = "状态长度必须介于 0 和 20 之间")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Length(min = 0, max = 50, message = "外部系统状态长度必须介于 0 和 50 之间")
    public String getExternalStatus() {
        return externalStatus;
    }

    public void setExternalStatus(String externalStatus) {
        this.externalStatus = externalStatus;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getLastSyncTime() {
        return lastSyncTime;
    }

    public void setLastSyncTime(Date lastSyncTime) {
        this.lastSyncTime = lastSyncTime;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getExternalUpdateTime() {
        return externalUpdateTime;
    }

    public void setExternalUpdateTime(Date externalUpdateTime) {
        this.externalUpdateTime = externalUpdateTime;
    }

    public Long getSyncVersion() {
        return syncVersion;
    }

    public void setSyncVersion(Long syncVersion) {
        this.syncVersion = syncVersion;
    }

    @Length(min = 0, max = 50, message = "数据来源长度必须介于 0 和 50 之间")
    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    public String getRawData() {
        return rawData;
    }

    public void setRawData(String rawData) {
        this.rawData = rawData;
    }
}
