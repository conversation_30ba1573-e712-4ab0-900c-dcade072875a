package com.thinkgem.jeesite.modules.sync.entity;

import java.util.Date;
import javax.validation.constraints.NotNull;
import org.hibernate.validator.constraints.Length;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.thinkgem.jeesite.common.persistence.DataEntity;

/**
 * 同步状态记录实体类
 * <AUTHOR>
 * @version 2025-01-01
 */
public class SyncRecord extends DataEntity<SyncRecord> {

    private static final long serialVersionUID = 1L;
    
    private String syncType;            // 同步类型
    private String dataSource;          // 数据源标识
    private Date lastSyncTime;          // 最后同步时间
    private Long lastSyncTimestamp;     // 最后同步时间戳
    private Long lastSyncVersion;       // 最后同步版本号
    private String syncStatus;          // 同步状态
    private Integer totalCount;         // 总记录数
    private Integer successCount;       // 成功记录数
    private Integer failedCount;        // 失败记录数
    private String errorMessage;        // 错误信息
    private Long syncDuration;          // 同步耗时(毫秒)
    private Date nextSyncTime;          // 下次同步时间

    public SyncRecord() {
        super();
        this.syncStatus = "success";
        this.totalCount = 0;
        this.successCount = 0;
        this.failedCount = 0;
        this.lastSyncVersion = 0L;
    }

    public SyncRecord(String id) {
        super(id);
    }

    @NotNull(message = "同步类型不能为空")
    @Length(min = 1, max = 50, message = "同步类型长度必须介于 1 和 50 之间")
    public String getSyncType() {
        return syncType;
    }

    public void setSyncType(String syncType) {
        this.syncType = syncType;
    }

    @NotNull(message = "数据源标识不能为空")
    @Length(min = 1, max = 100, message = "数据源标识长度必须介于 1 和 100 之间")
    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getLastSyncTime() {
        return lastSyncTime;
    }

    public void setLastSyncTime(Date lastSyncTime) {
        this.lastSyncTime = lastSyncTime;
    }

    public Long getLastSyncTimestamp() {
        return lastSyncTimestamp;
    }

    public void setLastSyncTimestamp(Long lastSyncTimestamp) {
        this.lastSyncTimestamp = lastSyncTimestamp;
    }

    public Long getLastSyncVersion() {
        return lastSyncVersion;
    }

    public void setLastSyncVersion(Long lastSyncVersion) {
        this.lastSyncVersion = lastSyncVersion;
    }

    @Length(min = 0, max = 20, message = "同步状态长度必须介于 0 和 20 之间")
    public String getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(String syncStatus) {
        this.syncStatus = syncStatus;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Integer successCount) {
        this.successCount = successCount;
    }

    public Integer getFailedCount() {
        return failedCount;
    }

    public void setFailedCount(Integer failedCount) {
        this.failedCount = failedCount;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public Long getSyncDuration() {
        return syncDuration;
    }

    public void setSyncDuration(Long syncDuration) {
        this.syncDuration = syncDuration;
    }

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    public Date getNextSyncTime() {
        return nextSyncTime;
    }

    public void setNextSyncTime(Date nextSyncTime) {
        this.nextSyncTime = nextSyncTime;
    }
}
