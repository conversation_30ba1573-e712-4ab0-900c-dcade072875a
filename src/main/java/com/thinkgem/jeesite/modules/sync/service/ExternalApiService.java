package com.thinkgem.jeesite.modules.sync.service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.thinkgem.jeesite.common.config.Global;
import com.thinkgem.jeesite.common.utils.DateUtils;

/**
 * 外部API调用服务
 * <AUTHOR>
 * @version 2025-01-01
 */
@Service
public class ExternalApiService {

    private static final Logger logger = LoggerFactory.getLogger(ExternalApiService.class);
    
    private RestTemplate restTemplate = new RestTemplate();

    /**
     * 获取增量主体信息数据
     * @param lastSyncTimestamp 最后同步时间戳
     * @param pageSize 分页大小
     * @param pageNum 页码
     * @return API响应结果
     */
    public ApiResponse<List<Map<String, Object>>> getIncrementalSubjectData(Long lastSyncTimestamp, 
                                                                           Integer pageSize, 
                                                                           Integer pageNum) {
        try {
            String baseUrl = Global.getConfig("subject.sync.api.base.url");
            String apiPath = Global.getConfig("subject.sync.api.subjects.path");
            String url = baseUrl + apiPath;
            
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("lastUpdateTime", lastSyncTimestamp);
            params.put("pageSize", pageSize);
            params.put("pageNum", pageNum);
            
            // 构建请求头
            HttpHeaders headers = buildRequestHeaders();
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            
            logger.info("调用外部API获取增量数据: url={}, params={}", url, JSON.toJSONString(params));
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                logger.info("外部API调用成功: {}", responseBody);
                
                // 解析响应结果
                JSONObject jsonResponse = JSON.parseObject(responseBody);
                return parseApiResponse(jsonResponse);
            } else {
                logger.error("外部API调用失败: status={}, body={}", response.getStatusCode(), response.getBody());
                return ApiResponse.error("API调用失败: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            logger.error("调用外部API异常", e);
            return ApiResponse.error("API调用异常: " + e.getMessage());
        }
    }

    /**
     * 获取指定时间范围的主体信息数据
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageSize 分页大小
     * @param pageNum 页码
     * @return API响应结果
     */
    public ApiResponse<List<Map<String, Object>>> getSubjectDataByTimeRange(Date startTime, 
                                                                           Date endTime,
                                                                           Integer pageSize, 
                                                                           Integer pageNum) {
        try {
            String baseUrl = Global.getConfig("subject.sync.api.base.url");
            String apiPath = Global.getConfig("subject.sync.api.subjects.range.path");
            String url = baseUrl + apiPath;
            
            // 构建请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("startTime", DateUtils.formatDateTime(startTime));
            params.put("endTime", DateUtils.formatDateTime(endTime));
            params.put("pageSize", pageSize);
            params.put("pageNum", pageNum);
            
            // 构建请求头
            HttpHeaders headers = buildRequestHeaders();
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(params, headers);
            
            logger.info("调用外部API获取时间范围数据: url={}, params={}", url, JSON.toJSONString(params));
            
            // 发送请求
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, String.class);
            
            if (response.getStatusCode().is2xxSuccessful()) {
                String responseBody = response.getBody();
                logger.info("外部API调用成功: {}", responseBody);
                
                // 解析响应结果
                JSONObject jsonResponse = JSON.parseObject(responseBody);
                return parseApiResponse(jsonResponse);
            } else {
                logger.error("外部API调用失败: status={}, body={}", response.getStatusCode(), response.getBody());
                return ApiResponse.error("API调用失败: " + response.getStatusCode());
            }
            
        } catch (Exception e) {
            logger.error("调用外部API异常", e);
            return ApiResponse.error("API调用异常: " + e.getMessage());
        }
    }

    /**
     * 构建请求头
     * @return 请求头
     */
    private HttpHeaders buildRequestHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        
        String authType = Global.getConfig("subject.sync.api.auth.type");
        if ("token".equals(authType)) {
            String token = Global.getConfig("subject.sync.api.token");
            headers.add("Authorization", "Bearer " + token);
        } else if ("basic".equals(authType)) {
            String username = Global.getConfig("subject.sync.api.username");
            String password = Global.getConfig("subject.sync.api.password");
            // 实现Basic认证逻辑
            String auth = username + ":" + password;
            String encodedAuth = java.util.Base64.getEncoder().encodeToString(auth.getBytes());
            headers.add("Authorization", "Basic " + encodedAuth);
        }
        
        return headers;
    }

    /**
     * 解析API响应结果
     * @param jsonResponse JSON响应
     * @return 解析后的响应结果
     */
    @SuppressWarnings("unchecked")
    private ApiResponse<List<Map<String, Object>>> parseApiResponse(JSONObject jsonResponse) {
        try {
            // 根据外部API的响应格式进行解析
            Integer code = jsonResponse.getInteger("code");
            String message = jsonResponse.getString("message");
            
            if (code != null && code == 200) {
                JSONObject data = jsonResponse.getJSONObject("data");
                if (data != null) {
                    List<Map<String, Object>> list = (List<Map<String, Object>>) data.get("list");
                    Integer total = data.getInteger("total");
                    Integer pageNum = data.getInteger("pageNum");
                    Integer pageSize = data.getInteger("pageSize");
                    
                    ApiResponse<List<Map<String, Object>>> response = ApiResponse.success(list);
                    response.setTotal(total);
                    response.setPageNum(pageNum);
                    response.setPageSize(pageSize);
                    
                    return response;
                } else {
                    return ApiResponse.success(null);
                }
            } else {
                return ApiResponse.error(message != null ? message : "API调用失败");
            }
        } catch (Exception e) {
            logger.error("解析API响应异常", e);
            return ApiResponse.error("响应解析异常: " + e.getMessage());
        }
    }

    /**
     * API响应结果封装类
     */
    public static class ApiResponse<T> {
        private boolean success;
        private String message;
        private T data;
        private Integer total;
        private Integer pageNum;
        private Integer pageSize;

        public static <T> ApiResponse<T> success(T data) {
            ApiResponse<T> response = new ApiResponse<>();
            response.success = true;
            response.data = data;
            return response;
        }

        public static <T> ApiResponse<T> error(String message) {
            ApiResponse<T> response = new ApiResponse<>();
            response.success = false;
            response.message = message;
            return response;
        }

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        public T getData() { return data; }
        public void setData(T data) { this.data = data; }
        public Integer getTotal() { return total; }
        public void setTotal(Integer total) { this.total = total; }
        public Integer getPageNum() { return pageNum; }
        public void setPageNum(Integer pageNum) { this.pageNum = pageNum; }
        public Integer getPageSize() { return pageSize; }
        public void setPageSize(Integer pageSize) { this.pageSize = pageSize; }
    }
}
