package com.thinkgem.jeesite.modules.sync.dao;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.thinkgem.jeesite.common.persistence.CrudDao;
import com.thinkgem.jeesite.common.persistence.annotation.MyBatisDao;
import com.thinkgem.jeesite.modules.sync.entity.SyncRecord;

/**
 * 同步记录DAO接口
 * <AUTHOR>
 * @version 2025-01-01
 */
@MyBatisDao
public interface SyncRecordDao extends CrudDao<SyncRecord> {

    /**
     * 根据同步类型和数据源查询同步记录
     * @param syncType 同步类型
     * @param dataSource 数据源
     * @return 同步记录
     */
    SyncRecord getBySyncTypeAndDataSource(@Param("syncType") String syncType, 
                                         @Param("dataSource") String dataSource);

    /**
     * 更新同步记录的最后同步时间
     * @param syncType 同步类型
     * @param dataSource 数据源
     * @param lastSyncTime 最后同步时间
     * @param lastSyncTimestamp 最后同步时间戳
     * @return 更新记录数
     */
    int updateLastSyncTime(@Param("syncType") String syncType,
                          @Param("dataSource") String dataSource,
                          @Param("lastSyncTime") Date lastSyncTime,
                          @Param("lastSyncTimestamp") Long lastSyncTimestamp);

    /**
     * 更新同步记录状态
     * @param syncType 同步类型
     * @param dataSource 数据源
     * @param syncStatus 同步状态
     * @param totalCount 总记录数
     * @param successCount 成功记录数
     * @param failedCount 失败记录数
     * @param errorMessage 错误信息
     * @param syncDuration 同步耗时
     * @return 更新记录数
     */
    int updateSyncStatus(@Param("syncType") String syncType,
                        @Param("dataSource") String dataSource,
                        @Param("syncStatus") String syncStatus,
                        @Param("totalCount") Integer totalCount,
                        @Param("successCount") Integer successCount,
                        @Param("failedCount") Integer failedCount,
                        @Param("errorMessage") String errorMessage,
                        @Param("syncDuration") Long syncDuration);

    /**
     * 查询正在运行的同步任务
     * @return 正在运行的同步记录列表
     */
    List<SyncRecord> findRunningSyncTasks();

    /**
     * 查询失败的同步任务
     * @param hours 小时数
     * @return 失败的同步记录列表
     */
    List<SyncRecord> findFailedSyncTasks(@Param("hours") int hours);

    /**
     * 重置超时的同步任务状态
     * @param timeoutMinutes 超时分钟数
     * @return 重置记录数
     */
    int resetTimeoutSyncTasks(@Param("timeoutMinutes") int timeoutMinutes);

    /**
     * 清理过期的同步记录
     * @param expireDate 过期时间
     * @return 清理记录数
     */
    int cleanExpiredRecords(@Param("expireDate") Date expireDate);
}
