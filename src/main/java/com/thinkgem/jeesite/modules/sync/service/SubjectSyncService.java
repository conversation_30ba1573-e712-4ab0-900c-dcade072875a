package com.thinkgem.jeesite.modules.sync.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.thinkgem.jeesite.common.config.Global;
import com.thinkgem.jeesite.common.utils.StringUtils;
import com.thinkgem.jeesite.modules.sync.dao.SubjectInfoDao;
import com.thinkgem.jeesite.modules.sync.dao.SyncRecordDao;
import com.thinkgem.jeesite.modules.sync.entity.SubjectInfo;
import com.thinkgem.jeesite.modules.sync.entity.SyncRecord;
import com.thinkgem.jeesite.modules.sync.service.DataCompareService.DataOperationResult;
import com.thinkgem.jeesite.modules.sync.service.ExternalApiService.ApiResponse;
import com.thinkgem.jeesite.modules.sys.entity.User;
import com.thinkgem.jeesite.modules.sys.utils.UserUtils;

/**
 * 主体同步核心服务
 * <AUTHOR>
 * @version 2025-01-01
 */
@Service
public class SubjectSyncService {

    private static final Logger logger = LoggerFactory.getLogger(SubjectSyncService.class);
    
    private static final String SYNC_TYPE = "subject_info";
    private static final String DATA_SOURCE = "external_system";

    @Autowired
    private ExternalApiService externalApiService;
    
    @Autowired
    private DataCompareService dataCompareService;
    
    @Autowired
    private SubjectInfoDao subjectInfoDao;
    
    @Autowired
    private SyncRecordDao syncRecordDao;

    /**
     * 执行增量同步
     * @return 同步结果
     */
    @Transactional(rollbackFor = Exception.class)
    public SyncResult executeIncrementalSync() {
        logger.info("开始执行主体信息增量同步");
        
        long startTime = System.currentTimeMillis();
        SyncResult syncResult = new SyncResult();
        
        try {
            // 1. 获取同步记录
            SyncRecord syncRecord = getSyncRecord();
            
            // 2. 更新同步状态为运行中
            updateSyncStatus(syncRecord, "running", null, null, null, null, null);
            
            // 3. 获取最后同步时间戳
            Long lastSyncTimestamp = getLastSyncTimestamp(syncRecord);
            logger.info("最后同步时间戳: {}", lastSyncTimestamp);
            
            // 4. 分页获取外部数据并处理
            int pageSize = Integer.parseInt(Global.getConfig("subject.sync.batch.size", "100"));
            int pageNum = 1;
            int totalProcessed = 0;
            int totalSuccess = 0;
            int totalFailed = 0;
            Long maxTimestamp = lastSyncTimestamp;
            
            while (true) {
                // 获取外部数据
                ApiResponse<List<Map<String, Object>>> apiResponse = 
                    externalApiService.getIncrementalSubjectData(lastSyncTimestamp, pageSize, pageNum);
                
                if (!apiResponse.isSuccess()) {
                    throw new RuntimeException("获取外部数据失败: " + apiResponse.getMessage());
                }
                
                List<Map<String, Object>> externalDataList = apiResponse.getData();
                if (CollectionUtils.isEmpty(externalDataList)) {
                    logger.info("第{}页无数据，同步完成", pageNum);
                    break;
                }
                
                logger.info("第{}页获取到{}条数据", pageNum, externalDataList.size());
                
                // 比较和分类数据
                DataOperationResult operationResult = 
                    dataCompareService.compareAndClassifyData(externalDataList, DATA_SOURCE);
                
                if (!operationResult.isSuccess()) {
                    throw new RuntimeException("数据比较失败: " + operationResult.getErrorMessage());
                }
                
                // 执行数据操作
                SyncOperationResult operationSyncResult = executeDataOperations(operationResult);
                
                // 累计统计
                totalProcessed += externalDataList.size();
                totalSuccess += operationSyncResult.getSuccessCount();
                totalFailed += operationSyncResult.getFailedCount();
                
                // 更新最大时间戳
                maxTimestamp = Math.max(maxTimestamp, getMaxTimestampFromData(externalDataList));
                
                // 检查是否还有更多数据
                if (externalDataList.size() < pageSize) {
                    logger.info("数据已全部获取完成");
                    break;
                }
                
                pageNum++;
            }
            
            // 5. 更新同步记录
            long syncDuration = System.currentTimeMillis() - startTime;
            updateSyncStatus(syncRecord, "success", totalProcessed, totalSuccess, totalFailed, null, syncDuration);
            updateLastSyncTimestamp(syncRecord, maxTimestamp);
            
            // 6. 设置同步结果
            syncResult.setSuccess(true);
            syncResult.setTotalCount(totalProcessed);
            syncResult.setSuccessCount(totalSuccess);
            syncResult.setFailedCount(totalFailed);
            syncResult.setSyncDuration(syncDuration);
            
            logger.info("主体信息增量同步完成 - 总数: {}, 成功: {}, 失败: {}, 耗时: {}ms", 
                totalProcessed, totalSuccess, totalFailed, syncDuration);
            
        } catch (Exception e) {
            logger.error("主体信息增量同步异常", e);
            
            // 更新同步状态为失败
            long syncDuration = System.currentTimeMillis() - startTime;
            SyncRecord syncRecord = getSyncRecord();
            updateSyncStatus(syncRecord, "failed", null, null, null, e.getMessage(), syncDuration);
            
            syncResult.setSuccess(false);
            syncResult.setErrorMessage(e.getMessage());
            syncResult.setSyncDuration(syncDuration);
        }
        
        return syncResult;
    }

    /**
     * 执行数据操作
     * @param operationResult 数据操作结果
     * @return 同步操作结果
     */
    private SyncOperationResult executeDataOperations(DataOperationResult operationResult) {
        SyncOperationResult syncOperationResult = new SyncOperationResult();
        int successCount = 0;
        int failedCount = 0;
        
        try {
            // 批量插入新数据
            if (!CollectionUtils.isEmpty(operationResult.getInsertList())) {
                logger.info("批量插入{}条新数据", operationResult.getInsertList().size());
                int insertCount = subjectInfoDao.batchInsert(operationResult.getInsertList());
                successCount += insertCount;
                logger.info("成功插入{}条数据", insertCount);
            }
            
            // 批量更新数据
            if (!CollectionUtils.isEmpty(operationResult.getUpdateList())) {
                logger.info("批量更新{}条数据", operationResult.getUpdateList().size());
                int updateCount = subjectInfoDao.batchUpdate(operationResult.getUpdateList());
                successCount += updateCount;
                logger.info("成功更新{}条数据", updateCount);
            }
            
            // 批量删除数据
            if (!CollectionUtils.isEmpty(operationResult.getDeleteList())) {
                logger.info("批量删除{}条数据", operationResult.getDeleteList().size());
                User currentUser = UserUtils.getUser();
                int deleteCount = subjectInfoDao.batchDeleteByExternalIds(
                    operationResult.getDeleteList(), 
                    currentUser.getId(), 
                    new Date());
                successCount += deleteCount;
                logger.info("成功删除{}条数据", deleteCount);
            }
            
        } catch (Exception e) {
            logger.error("执行数据操作异常", e);
            failedCount = operationResult.getInsertList().size() + 
                         operationResult.getUpdateList().size() + 
                         operationResult.getDeleteList().size();
        }
        
        syncOperationResult.setSuccessCount(successCount);
        syncOperationResult.setFailedCount(failedCount);
        
        return syncOperationResult;
    }

    /**
     * 获取同步记录
     * @return 同步记录
     */
    private SyncRecord getSyncRecord() {
        SyncRecord syncRecord = syncRecordDao.getBySyncTypeAndDataSource(SYNC_TYPE, DATA_SOURCE);
        if (syncRecord == null) {
            // 创建新的同步记录
            syncRecord = new SyncRecord();
            syncRecord.setSyncType(SYNC_TYPE);
            syncRecord.setDataSource(DATA_SOURCE);
            syncRecord.setLastSyncTimestamp(0L);
            syncRecord.preInsert();
            syncRecordDao.insert(syncRecord);
        }
        return syncRecord;
    }

    /**
     * 获取最后同步时间戳
     * @param syncRecord 同步记录
     * @return 最后同步时间戳
     */
    private Long getLastSyncTimestamp(SyncRecord syncRecord) {
        Long lastSyncTimestamp = syncRecord.getLastSyncTimestamp();
        if (lastSyncTimestamp == null || lastSyncTimestamp == 0) {
            // 如果没有同步记录，获取数据库中最大的外部更新时间
            Date maxExternalUpdateTime = subjectInfoDao.getMaxExternalUpdateTime(DATA_SOURCE);
            if (maxExternalUpdateTime != null) {
                lastSyncTimestamp = maxExternalUpdateTime.getTime();
            } else {
                // 默认从30天前开始同步
                lastSyncTimestamp = System.currentTimeMillis() - 30 * 24 * 60 * 60 * 1000L;
            }
        }
        return lastSyncTimestamp;
    }

    /**
     * 从数据中获取最大时间戳
     * @param dataList 数据列表
     * @return 最大时间戳
     */
    private Long getMaxTimestampFromData(List<Map<String, Object>> dataList) {
        Long maxTimestamp = 0L;
        for (Map<String, Object> data : dataList) {
            Object updateTimeObj = data.get("updateTime");
            if (updateTimeObj != null) {
                Long timestamp = null;
                if (updateTimeObj instanceof Long) {
                    timestamp = (Long) updateTimeObj;
                } else if (updateTimeObj instanceof Date) {
                    timestamp = ((Date) updateTimeObj).getTime();
                } else if (updateTimeObj instanceof String) {
                    try {
                        timestamp = Long.parseLong((String) updateTimeObj);
                    } catch (NumberFormatException e) {
                        // 忽略解析错误
                    }
                }
                if (timestamp != null && timestamp > maxTimestamp) {
                    maxTimestamp = timestamp;
                }
            }
        }
        return maxTimestamp;
    }

    /**
     * 更新同步状态
     */
    private void updateSyncStatus(SyncRecord syncRecord, String syncStatus, Integer totalCount, 
                                 Integer successCount, Integer failedCount, String errorMessage, Long syncDuration) {
        syncRecordDao.updateSyncStatus(SYNC_TYPE, DATA_SOURCE, syncStatus, totalCount, 
                                      successCount, failedCount, errorMessage, syncDuration);
    }

    /**
     * 更新最后同步时间戳
     */
    private void updateLastSyncTimestamp(SyncRecord syncRecord, Long lastSyncTimestamp) {
        Date lastSyncTime = new Date(lastSyncTimestamp);
        syncRecordDao.updateLastSyncTime(SYNC_TYPE, DATA_SOURCE, lastSyncTime, lastSyncTimestamp);
    }

    /**
     * 同步结果封装类
     */
    public static class SyncResult {
        private boolean success;
        private String errorMessage;
        private int totalCount;
        private int successCount;
        private int failedCount;
        private long syncDuration;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
        public long getSyncDuration() { return syncDuration; }
        public void setSyncDuration(long syncDuration) { this.syncDuration = syncDuration; }
    }

    /**
     * 同步操作结果封装类
     */
    private static class SyncOperationResult {
        private int successCount;
        private int failedCount;

        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }
    }
}
