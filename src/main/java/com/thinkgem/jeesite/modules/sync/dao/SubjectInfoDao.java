package com.thinkgem.jeesite.modules.sync.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Param;
import com.thinkgem.jeesite.common.persistence.CrudDao;
import com.thinkgem.jeesite.common.persistence.annotation.MyBatisDao;
import com.thinkgem.jeesite.modules.sync.entity.SubjectInfo;

/**
 * 主体信息DAO接口
 * <AUTHOR>
 * @version 2025-01-01
 */
@MyBatisDao
public interface SubjectInfoDao extends CrudDao<SubjectInfo> {

    /**
     * 根据外部系统ID查询主体信息
     * @param externalId 外部系统ID
     * @return 主体信息
     */
    SubjectInfo getByExternalId(@Param("externalId") String externalId);

    /**
     * 根据外部系统ID列表批量查询主体信息
     * @param externalIds 外部系统ID列表
     * @return 主体信息列表
     */
    List<SubjectInfo> findByExternalIds(@Param("externalIds") List<String> externalIds);

    /**
     * 批量插入主体信息
     * @param subjectInfoList 主体信息列表
     * @return 插入记录数
     */
    int batchInsert(@Param("list") List<SubjectInfo> subjectInfoList);

    /**
     * 批量更新主体信息
     * @param subjectInfoList 主体信息列表
     * @return 更新记录数
     */
    int batchUpdate(@Param("list") List<SubjectInfo> subjectInfoList);

    /**
     * 根据外部系统ID批量逻辑删除
     * @param externalIds 外部系统ID列表
     * @param updateBy 更新人
     * @param updateDate 更新时间
     * @return 删除记录数
     */
    int batchDeleteByExternalIds(@Param("externalIds") List<String> externalIds, 
                                @Param("updateBy") String updateBy, 
                                @Param("updateDate") Date updateDate);

    /**
     * 获取最大的外部更新时间
     * @param dataSource 数据源
     * @return 最大外部更新时间
     */
    Date getMaxExternalUpdateTime(@Param("dataSource") String dataSource);

    /**
     * 获取最大的同步版本号
     * @param dataSource 数据源
     * @return 最大同步版本号
     */
    Long getMaxSyncVersion(@Param("dataSource") String dataSource);

    /**
     * 根据同步时间范围查询主体信息
     * @param dataSource 数据源
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 主体信息列表
     */
    List<SubjectInfo> findBySyncTimeRange(@Param("dataSource") String dataSource,
                                         @Param("startTime") Date startTime,
                                         @Param("endTime") Date endTime);

    /**
     * 统计同步数据
     * @param dataSource 数据源
     * @return 统计结果
     */
    Map<String, Object> countSyncData(@Param("dataSource") String dataSource);

    /**
     * 清理过期的同步数据
     * @param dataSource 数据源
     * @param expireDate 过期时间
     * @return 清理记录数
     */
    int cleanExpiredData(@Param("dataSource") String dataSource, 
                        @Param("expireDate") Date expireDate);
}
