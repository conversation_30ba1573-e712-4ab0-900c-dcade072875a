package com.thinkgem.jeesite.modules.sync.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.alibaba.fastjson.JSON;
import com.thinkgem.jeesite.common.utils.DateUtils;
import com.thinkgem.jeesite.common.utils.IdGen;
import com.thinkgem.jeesite.modules.sync.dao.SubjectInfoDao;
import com.thinkgem.jeesite.modules.sync.entity.SubjectInfo;
import com.thinkgem.jeesite.modules.sys.entity.User;
import com.thinkgem.jeesite.modules.sys.utils.UserUtils;

/**
 * 数据比较服务
 * <AUTHOR>
 * @version 2025-01-01
 */
@Service
public class DataCompareService {

    private static final Logger logger = LoggerFactory.getLogger(DataCompareService.class);

    @Autowired
    private SubjectInfoDao subjectInfoDao;

    /**
     * 比较并分类外部数据
     * @param externalDataList 外部数据列表
     * @param dataSource 数据源
     * @return 数据操作结果
     */
    public DataOperationResult compareAndClassifyData(List<Map<String, Object>> externalDataList, String dataSource) {
        logger.info("开始比较和分类数据，外部数据数量: {}", externalDataList.size());
        
        DataOperationResult result = new DataOperationResult();
        
        try {
            // 提取外部数据的ID列表
            List<String> externalIds = externalDataList.stream()
                .map(data -> String.valueOf(data.get("id")))
                .collect(Collectors.toList());
            
            // 查询本地已存在的数据
            List<SubjectInfo> existingDataList = subjectInfoDao.findByExternalIds(externalIds);
            Map<String, SubjectInfo> existingDataMap = existingDataList.stream()
                .collect(Collectors.toMap(SubjectInfo::getExternalId, item -> item));
            
            // 分类处理数据
            for (Map<String, Object> externalData : externalDataList) {
                String externalId = String.valueOf(externalData.get("id"));
                SubjectInfo existingData = existingDataMap.get(externalId);
                
                if (existingData == null) {
                    // 新增数据
                    SubjectInfo newSubjectInfo = convertToSubjectInfo(externalData, dataSource);
                    result.getInsertList().add(newSubjectInfo);
                } else {
                    // 检查是否需要更新
                    if (needUpdate(existingData, externalData)) {
                        SubjectInfo updatedSubjectInfo = updateSubjectInfo(existingData, externalData);
                        result.getUpdateList().add(updatedSubjectInfo);
                    } else {
                        result.getUnchangedList().add(existingData);
                    }
                }
            }
            
            // 处理删除的数据（如果外部系统支持删除标识）
            processDeletedData(result, externalDataList, dataSource);
            
            logger.info("数据分类完成 - 新增: {}, 更新: {}, 删除: {}, 未变更: {}", 
                result.getInsertList().size(), 
                result.getUpdateList().size(), 
                result.getDeleteList().size(),
                result.getUnchangedList().size());
            
        } catch (Exception e) {
            logger.error("数据比较和分类异常", e);
            result.setSuccess(false);
            result.setErrorMessage("数据比较异常: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 将外部数据转换为SubjectInfo实体
     * @param externalData 外部数据
     * @param dataSource 数据源
     * @return SubjectInfo实体
     */
    private SubjectInfo convertToSubjectInfo(Map<String, Object> externalData, String dataSource) {
        SubjectInfo subjectInfo = new SubjectInfo();
        
        // 设置基础信息
        subjectInfo.setId(IdGen.nextId());
        subjectInfo.setExternalId(String.valueOf(externalData.get("id")));
        subjectInfo.setSubjectName(String.valueOf(externalData.get("name")));
        subjectInfo.setSubjectType(String.valueOf(externalData.get("type")));
        subjectInfo.setSocialCreditCode(String.valueOf(externalData.get("socialCreditCode")));
        subjectInfo.setLegalPerson(String.valueOf(externalData.get("legalPerson")));
        subjectInfo.setContactPhone(String.valueOf(externalData.get("contactPhone")));
        subjectInfo.setContactAddress(String.valueOf(externalData.get("contactAddress")));
        subjectInfo.setBusinessScope(String.valueOf(externalData.get("businessScope")));
        
        // 处理日期字段
        Object registrationDateObj = externalData.get("registrationDate");
        if (registrationDateObj != null) {
            subjectInfo.setRegistrationDate(parseDate(registrationDateObj));
        }
        
        Object updateTimeObj = externalData.get("updateTime");
        if (updateTimeObj != null) {
            subjectInfo.setExternalUpdateTime(parseDate(updateTimeObj));
        }
        
        // 设置状态和版本信息
        subjectInfo.setStatus(String.valueOf(externalData.get("status")));
        subjectInfo.setExternalStatus(String.valueOf(externalData.get("externalStatus")));
        subjectInfo.setDataSource(dataSource);
        subjectInfo.setRawData(JSON.toJSONString(externalData));
        
        // 设置同步信息
        Date now = new Date();
        subjectInfo.setLastSyncTime(now);
        subjectInfo.setSyncVersion(System.currentTimeMillis());
        
        // 设置审计信息
        User currentUser = UserUtils.getUser();
        subjectInfo.setCreateBy(currentUser);
        subjectInfo.setCreateDate(now);
        subjectInfo.setUpdateBy(currentUser);
        subjectInfo.setUpdateDate(now);
        subjectInfo.preInsert();
        
        return subjectInfo;
    }

    /**
     * 更新SubjectInfo实体
     * @param existingData 现有数据
     * @param externalData 外部数据
     * @return 更新后的SubjectInfo实体
     */
    private SubjectInfo updateSubjectInfo(SubjectInfo existingData, Map<String, Object> externalData) {
        // 更新字段
        existingData.setSubjectName(String.valueOf(externalData.get("name")));
        existingData.setSubjectType(String.valueOf(externalData.get("type")));
        existingData.setSocialCreditCode(String.valueOf(externalData.get("socialCreditCode")));
        existingData.setLegalPerson(String.valueOf(externalData.get("legalPerson")));
        existingData.setContactPhone(String.valueOf(externalData.get("contactPhone")));
        existingData.setContactAddress(String.valueOf(externalData.get("contactAddress")));
        existingData.setBusinessScope(String.valueOf(externalData.get("businessScope")));
        
        // 处理日期字段
        Object registrationDateObj = externalData.get("registrationDate");
        if (registrationDateObj != null) {
            existingData.setRegistrationDate(parseDate(registrationDateObj));
        }
        
        Object updateTimeObj = externalData.get("updateTime");
        if (updateTimeObj != null) {
            existingData.setExternalUpdateTime(parseDate(updateTimeObj));
        }
        
        // 更新状态和版本信息
        existingData.setStatus(String.valueOf(externalData.get("status")));
        existingData.setExternalStatus(String.valueOf(externalData.get("externalStatus")));
        existingData.setRawData(JSON.toJSONString(externalData));
        
        // 更新同步信息
        Date now = new Date();
        existingData.setLastSyncTime(now);
        existingData.setSyncVersion(System.currentTimeMillis());
        
        // 设置审计信息
        User currentUser = UserUtils.getUser();
        existingData.setUpdateBy(currentUser);
        existingData.setUpdateDate(now);
        existingData.preUpdate();
        
        return existingData;
    }

    /**
     * 判断是否需要更新
     * @param existingData 现有数据
     * @param externalData 外部数据
     * @return 是否需要更新
     */
    private boolean needUpdate(SubjectInfo existingData, Map<String, Object> externalData) {
        // 比较关键字段是否发生变化
        if (!StringUtils.equals(existingData.getSubjectName(), String.valueOf(externalData.get("name")))) {
            return true;
        }
        if (!StringUtils.equals(existingData.getSubjectType(), String.valueOf(externalData.get("type")))) {
            return true;
        }
        if (!StringUtils.equals(existingData.getSocialCreditCode(), String.valueOf(externalData.get("socialCreditCode")))) {
            return true;
        }
        if (!StringUtils.equals(existingData.getLegalPerson(), String.valueOf(externalData.get("legalPerson")))) {
            return true;
        }
        if (!StringUtils.equals(existingData.getContactPhone(), String.valueOf(externalData.get("contactPhone")))) {
            return true;
        }
        if (!StringUtils.equals(existingData.getContactAddress(), String.valueOf(externalData.get("contactAddress")))) {
            return true;
        }
        if (!StringUtils.equals(existingData.getStatus(), String.valueOf(externalData.get("status")))) {
            return true;
        }
        
        // 比较外部更新时间
        Object updateTimeObj = externalData.get("updateTime");
        if (updateTimeObj != null) {
            Date externalUpdateTime = parseDate(updateTimeObj);
            if (existingData.getExternalUpdateTime() == null || 
                externalUpdateTime.after(existingData.getExternalUpdateTime())) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 处理删除的数据
     * @param result 数据操作结果
     * @param externalDataList 外部数据列表
     * @param dataSource 数据源
     */
    private void processDeletedData(DataOperationResult result, List<Map<String, Object>> externalDataList, String dataSource) {
        // 如果外部系统支持删除标识，可以在这里处理
        // 例如：外部数据中包含 isDeleted 字段
        for (Map<String, Object> externalData : externalDataList) {
            Object isDeletedObj = externalData.get("isDeleted");
            if (isDeletedObj != null && Boolean.parseBoolean(String.valueOf(isDeletedObj))) {
                String externalId = String.valueOf(externalData.get("id"));
                result.getDeleteList().add(externalId);
            }
        }
    }

    /**
     * 解析日期
     * @param dateObj 日期对象
     * @return Date
     */
    private Date parseDate(Object dateObj) {
        if (dateObj == null) {
            return null;
        }
        
        if (dateObj instanceof Date) {
            return (Date) dateObj;
        }
        
        if (dateObj instanceof Long) {
            return new Date((Long) dateObj);
        }
        
        if (dateObj instanceof String) {
            String dateStr = (String) dateObj;
            try {
                // 尝试多种日期格式
                if (dateStr.contains("T")) {
                    return DateUtils.parseDate(dateStr, "yyyy-MM-dd'T'HH:mm:ss");
                } else if (dateStr.contains(" ")) {
                    return DateUtils.parseDate(dateStr, "yyyy-MM-dd HH:mm:ss");
                } else {
                    return DateUtils.parseDate(dateStr, "yyyy-MM-dd");
                }
            } catch (Exception e) {
                logger.warn("日期解析失败: {}", dateStr);
                return null;
            }
        }
        
        return null;
    }

    /**
     * 数据操作结果封装类
     */
    public static class DataOperationResult {
        private boolean success = true;
        private String errorMessage;
        private List<SubjectInfo> insertList = new ArrayList<>();
        private List<SubjectInfo> updateList = new ArrayList<>();
        private List<String> deleteList = new ArrayList<>();
        private List<SubjectInfo> unchangedList = new ArrayList<>();

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
        public List<SubjectInfo> getInsertList() { return insertList; }
        public void setInsertList(List<SubjectInfo> insertList) { this.insertList = insertList; }
        public List<SubjectInfo> getUpdateList() { return updateList; }
        public void setUpdateList(List<SubjectInfo> updateList) { this.updateList = updateList; }
        public List<String> getDeleteList() { return deleteList; }
        public void setDeleteList(List<String> deleteList) { this.deleteList = deleteList; }
        public List<SubjectInfo> getUnchangedList() { return unchangedList; }
        public void setUnchangedList(List<SubjectInfo> unchangedList) { this.unchangedList = unchangedList; }
    }
}
