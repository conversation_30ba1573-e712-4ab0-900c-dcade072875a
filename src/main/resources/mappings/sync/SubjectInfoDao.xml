<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.thinkgem.jeesite.modules.sync.dao.SubjectInfoDao">
    
    <sql id="subjectInfoColumns">
        a.id AS "id",
        a.external_id AS "externalId",
        a.subject_name AS "subjectName",
        a.subject_type AS "subjectType",
        a.social_credit_code AS "socialCreditCode",
        a.legal_person AS "legalPerson",
        a.contact_phone AS "contactPhone",
        a.contact_address AS "contactAddress",
        a.business_scope AS "businessScope",
        a.registration_date AS "registrationDate",
        a.status AS "status",
        a.external_status AS "externalStatus",
        a.last_sync_time AS "lastSyncTime",
        a.external_update_time AS "externalUpdateTime",
        a.sync_version AS "syncVersion",
        a.data_source AS "dataSource",
        a.raw_data AS "rawData",
        a.create_by AS "createBy.id",
        a.create_date AS "createDate",
        a.update_by AS "updateBy.id",
        a.update_date AS "updateDate",
        a.remarks AS "remarks",
        a.del_flag AS "delFlag"
    </sql>
    
    <sql id="subjectInfoJoins">
    </sql>
    
    <select id="get" resultType="SubjectInfo">
        SELECT 
            <include refid="subjectInfoColumns"/>
        FROM bas_subject_info a
        <include refid="subjectInfoJoins"/>
        WHERE a.id = #{id}
    </select>
    
    <select id="getByExternalId" resultType="SubjectInfo">
        SELECT 
            <include refid="subjectInfoColumns"/>
        FROM bas_subject_info a
        <include refid="subjectInfoJoins"/>
        WHERE a.external_id = #{externalId} AND a.del_flag = #{DEL_FLAG_NORMAL}
    </select>
    
    <select id="findByExternalIds" resultType="SubjectInfo">
        SELECT 
            <include refid="subjectInfoColumns"/>
        FROM bas_subject_info a
        <include refid="subjectInfoJoins"/>
        WHERE a.del_flag = #{DEL_FLAG_NORMAL}
        AND a.external_id IN
        <foreach collection="externalIds" item="externalId" open="(" separator="," close=")">
            #{externalId}
        </foreach>
    </select>
    
    <select id="findList" resultType="SubjectInfo">
        SELECT 
            <include refid="subjectInfoColumns"/>
        FROM bas_subject_info a
        <include refid="subjectInfoJoins"/>
        <where>
            a.del_flag = #{DEL_FLAG_NORMAL}
            <if test="externalId != null and externalId != ''">
                AND a.external_id = #{externalId}
            </if>
            <if test="subjectName != null and subjectName != ''">
                AND a.subject_name LIKE CONCAT('%',#{subjectName},'%')
            </if>
            <if test="subjectType != null and subjectType != ''">
                AND a.subject_type = #{subjectType}
            </if>
            <if test="dataSource != null and dataSource != ''">
                AND a.data_source = #{dataSource}
            </if>
        </where>
        <choose>
            <when test="page !=null and page.orderBy != null and page.orderBy != ''">
                ORDER BY ${page.orderBy}
            </when>
            <otherwise>
                ORDER BY a.update_date DESC
            </otherwise>
        </choose>
    </select>
    
    <select id="findAllList" resultType="SubjectInfo">
        SELECT 
            <include refid="subjectInfoColumns"/>
        FROM bas_subject_info a
        <include refid="subjectInfoJoins"/>
        <where>
            a.del_flag = #{DEL_FLAG_NORMAL}
        </where>        
        ORDER BY a.update_date DESC
    </select>
    
    <insert id="insert">
        INSERT INTO bas_subject_info(
            id,
            external_id,
            subject_name,
            subject_type,
            social_credit_code,
            legal_person,
            contact_phone,
            contact_address,
            business_scope,
            registration_date,
            status,
            external_status,
            last_sync_time,
            external_update_time,
            sync_version,
            data_source,
            raw_data,
            create_by,
            create_date,
            update_by,
            update_date,
            remarks,
            del_flag
        ) VALUES (
            #{id},
            #{externalId},
            #{subjectName},
            #{subjectType},
            #{socialCreditCode},
            #{legalPerson},
            #{contactPhone},
            #{contactAddress},
            #{businessScope},
            #{registrationDate},
            #{status},
            #{externalStatus},
            #{lastSyncTime},
            #{externalUpdateTime},
            #{syncVersion},
            #{dataSource},
            #{rawData},
            #{createBy.id},
            #{createDate},
            #{updateBy.id},
            #{updateDate},
            #{remarks},
            #{delFlag}
        )
    </insert>
    
    <insert id="batchInsert">
        INSERT INTO bas_subject_info(
            id, external_id, subject_name, subject_type, social_credit_code,
            legal_person, contact_phone, contact_address, business_scope,
            registration_date, status, external_status, last_sync_time,
            external_update_time, sync_version, data_source, raw_data,
            create_by, create_date, update_by, update_date, remarks, del_flag
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.id}, #{item.externalId}, #{item.subjectName}, #{item.subjectType},
                #{item.socialCreditCode}, #{item.legalPerson}, #{item.contactPhone},
                #{item.contactAddress}, #{item.businessScope}, #{item.registrationDate},
                #{item.status}, #{item.externalStatus}, #{item.lastSyncTime},
                #{item.externalUpdateTime}, #{item.syncVersion}, #{item.dataSource},
                #{item.rawData}, #{item.createBy.id}, #{item.createDate},
                #{item.updateBy.id}, #{item.updateDate}, #{item.remarks}, #{item.delFlag}
            )
        </foreach>
    </insert>
    
    <update id="update">
        UPDATE bas_subject_info SET 
            subject_name = #{subjectName},
            subject_type = #{subjectType},
            social_credit_code = #{socialCreditCode},
            legal_person = #{legalPerson},
            contact_phone = #{contactPhone},
            contact_address = #{contactAddress},
            business_scope = #{businessScope},
            registration_date = #{registrationDate},
            status = #{status},
            external_status = #{externalStatus},
            last_sync_time = #{lastSyncTime},
            external_update_time = #{externalUpdateTime},
            sync_version = #{syncVersion},
            raw_data = #{rawData},
            update_by = #{updateBy.id},
            update_date = #{updateDate},
            remarks = #{remarks}
        WHERE id = #{id}
    </update>
    
    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE bas_subject_info SET 
                subject_name = #{item.subjectName},
                subject_type = #{item.subjectType},
                social_credit_code = #{item.socialCreditCode},
                legal_person = #{item.legalPerson},
                contact_phone = #{item.contactPhone},
                contact_address = #{item.contactAddress},
                business_scope = #{item.businessScope},
                registration_date = #{item.registrationDate},
                status = #{item.status},
                external_status = #{item.externalStatus},
                last_sync_time = #{item.lastSyncTime},
                external_update_time = #{item.externalUpdateTime},
                sync_version = #{item.syncVersion},
                raw_data = #{item.rawData},
                update_by = #{item.updateBy.id},
                update_date = #{item.updateDate},
                remarks = #{item.remarks}
            WHERE external_id = #{item.externalId}
        </foreach>
    </update>
    
    <update id="delete">
        UPDATE bas_subject_info SET 
            del_flag = #{DEL_FLAG_DELETE}
        WHERE id = #{id}
    </update>
    
    <update id="batchDeleteByExternalIds">
        UPDATE bas_subject_info SET 
            del_flag = #{DEL_FLAG_DELETE},
            update_by = #{updateBy},
            update_date = #{updateDate}
        WHERE external_id IN
        <foreach collection="externalIds" item="externalId" open="(" separator="," close=")">
            #{externalId}
        </foreach>
    </update>
    
    <select id="getMaxExternalUpdateTime" resultType="java.util.Date">
        SELECT MAX(external_update_time) 
        FROM bas_subject_info 
        WHERE data_source = #{dataSource} AND del_flag = #{DEL_FLAG_NORMAL}
    </select>
    
    <select id="getMaxSyncVersion" resultType="java.lang.Long">
        SELECT MAX(sync_version) 
        FROM bas_subject_info 
        WHERE data_source = #{dataSource} AND del_flag = #{DEL_FLAG_NORMAL}
    </select>
    
    <select id="findBySyncTimeRange" resultType="SubjectInfo">
        SELECT 
            <include refid="subjectInfoColumns"/>
        FROM bas_subject_info a
        WHERE a.data_source = #{dataSource} 
        AND a.del_flag = #{DEL_FLAG_NORMAL}
        AND a.last_sync_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY a.last_sync_time DESC
    </select>
    
    <select id="countSyncData" resultType="java.util.Map">
        SELECT 
            COUNT(*) as totalCount,
            COUNT(CASE WHEN status = '1' THEN 1 END) as activeCount,
            COUNT(CASE WHEN status = '0' THEN 1 END) as inactiveCount,
            MAX(last_sync_time) as lastSyncTime,
            MIN(last_sync_time) as firstSyncTime
        FROM bas_subject_info 
        WHERE data_source = #{dataSource} AND del_flag = #{DEL_FLAG_NORMAL}
    </select>
    
    <delete id="cleanExpiredData">
        DELETE FROM bas_subject_info 
        WHERE data_source = #{dataSource} 
        AND del_flag = #{DEL_FLAG_DELETE}
        AND update_date &lt; #{expireDate}
    </delete>
    
</mapper>
